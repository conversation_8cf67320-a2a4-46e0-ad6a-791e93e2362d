import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_mesh/cubit/user/avatar_upload/avatar_upload_cubit.dart';
import 'package:toii_mesh/cubit/user/edit_profile/edit_profile_cubit.dart';
import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/model/user/user_model.dart';
import 'package:toii_mesh/screen/chats/widgets/user_info_widget.dart';
import 'package:toii_mesh/screen/user_setting/my_profile/widget/avatar_selection_bottom_sheet.dart';
import 'package:toii_mesh/widget/button/button.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
import 'package:toii_mesh/widget/snack_bar/snackbar.dart';
import 'package:toii_mesh/widget/text_field.dart/text_field.dart';

class MyProfileScreen extends StatefulWidget {
  const MyProfileScreen({super.key});

  @override
  State<MyProfileScreen> createState() => _MyProfileScreenState();
}

class _MyProfileScreenState extends State<MyProfileScreen> {
  late TextEditingController _displayNameController;
  late TextEditingController _usernameController;
  late FocusNode _displayNameFocusNode;
  late FocusNode _usernameFocusNode;
  final _avatarUploadCubit = AvatarUploadCubit();
  final EditProfileCubit _editProfileCubit = EditProfileCubit();

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController();
    _usernameController = TextEditingController();
    _displayNameFocusNode = FocusNode();
    _usernameFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _usernameController.dispose();
    _displayNameFocusNode.dispose();
    _usernameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => GetIt.instance<ProfileCubit>(),
        ),
        BlocProvider(
          create: (context) => _avatarUploadCubit,
        ),
        BlocProvider(
          create: (context) => _editProfileCubit,
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFFFFFFFF), // Neutrals/Neutral 50 [day]
        body: BlocListener<EditProfileCubit, EditProfileState>(
          listener: (context, state) {
            if (state.status.isSuccess) {
              context.showSnackbar(message: "Edit profile successfully");
              GetIt.instance<ProfileCubit>().getProfile();
              context.pop();
            } else if (state.status.isFailure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    state.errorMessage ?? 'Failed to update profile',
                  ),
                ),
              );
            }
          },
          child: BlocBuilder<ProfileCubit, ProfileState>(
            bloc: GetIt.instance<ProfileCubit>(),
            builder: (context, state) {
              final user = state.userModel;
              final displayName = user?.fullName ?? '-';
              final username = user?.username ?? '-';

              // Set initial values if not already set
              if (_displayNameController.text.isEmpty) {
                _displayNameController.text = displayName;
              }
              if (_usernameController.text.isEmpty) {
                _usernameController.text = username;
              }

              _editProfileCubit.initializeProfile(user!);

              return SafeArea(
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              child: Row(
                                children: [
                                  // Back button
                                  GestureDetector(
                                    onTap: () => context.pop(),
                                    child: Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: context.themeData.black50,
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                      child: Center(
                                        child: Icon(
                                          Icons.arrow_back_ios,
                                          size: 14,
                                          color: context.themeData.neutral800,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const Spacer(),
                                ],
                              ),
                            ),
                            const SizedBox(height: 50),

                            // Profile header section
                            _buildProfileHeader(displayName, user),

                            const SizedBox(height: 24),

                            // XMTP User Info Widget
                            const UserInfoWidget(),

                            const SizedBox(height: 24),

                            // Form fields
                            _buildFormFields(),

                            const SizedBox(height: 100),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildUpdateButton(),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader(String displayName, UserModel? user) {
    return BlocListener<AvatarUploadCubit, AvatarUploadState>(
      listener: (context, state) {
        if (state.status.isLoading) {
          EasyLoading.show();
        }
        if (state.status.isSuccess && state.localImagePath != null) {
          EasyLoading.dismiss();
          context.pop();
          // Update the edit profile cubit with the selected image path
          context.read<EditProfileCubit>().updateAvatarWithKey(
                user!.id,
                state.mediaKey!,
              );

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Avatar updated successfully')),
          );
          GetIt.instance<ProfileCubit>().getProfile();
        } else if (state.status.isFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.errorMessage ?? 'Failed to select avatar',
              ),
            ),
          );
        }
      },
      child: Column(
        children: [
          // Avatar with camera icon
          Stack(
            children: [
              // Avatar
              (user?.avatarUrl ?? "").isNotEmpty
                  ? Container(
                      width: 104,
                      height: 104,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(52),
                        image: DecorationImage(
                          image: NetworkImage(user!.avatarUrl!),
                          fit: BoxFit.cover,
                        ),
                      ),
                    )
                  : ClipRRect(
                      borderRadius: BorderRadius.circular(50),
                      child: Container(
                        width: 104,
                        height: 104,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(52),
                          image: DecorationImage(
                            image: AssetImage(
                                Assets.images.avatarKristinWatson.path),
                            fit: BoxFit.cover,
                          ),
                        ),
                      )),
              // Camera icon
              Positioned(
                right: 4,
                bottom: 3,
                child: GestureDetector(
                  onTap: () {
                    _selectAvatar();
                  },
                  child: Container(
                    width: 22,
                    height: 22,
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6C4EFF), // Primary/blue_500
                      borderRadius: BorderRadius.circular(100),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      size: 14,
                      color: Color(0xE6FFFFFF), // White transperant/White-900
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // User name
          Text(
            displayName,
            style: titleLarge.copyWith(
              color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Display name field
          TTextField(
            labelText: 'Displayname',
            textController: _displayNameController,
            focusNode: _displayNameFocusNode,
            onChanged: (value) {
              _editProfileCubit.updateDisplayName(value);
            },
            fillColor: Colors.white,
            textStyle: bodyLarge.copyWith(
              color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
            ),
          ),

          const SizedBox(height: 16),

          // Username field
          TTextField(
            labelText: 'Username',
            textController: _usernameController,
            focusNode: _usernameFocusNode,
            fillColor: Colors.white,
            onChanged: (value) {
              _editProfileCubit.updateUsername(value);
            },
            textStyle: bodyLarge.copyWith(
              color: const Color(0xFF292929), // Neutrals/Neutral 800 [day]
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
          width: 358,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFFF6F6F6), // Neutrals/Neutral 100 [day]
            borderRadius: BorderRadius.circular(48),
          ),
          child: TSButton.primary(
              title: "Update Profile",
              onPressed: () {
                _saveProfile(GetIt.instance<ProfileCubit>().state.userModel!);
              })),
    );
  }

  void _saveProfile(UserModel user) {
    _editProfileCubit.saveProfile(user.id);
  }

  void _selectAvatar() {
    AvatarSelectionBottomSheet.show(
      context: context,
      onGalleryTap: () => _avatarUploadCubit.pickImageFromGallery(),
      onCameraTap: () => _avatarUploadCubit.pickImageFromCamera(),
      onAIGenerateTap: () {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Coming soon')));
      },
    );
  }

  Widget _buildDeleteAccountOption() {
    return Container(
      width: 390,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          // Close circle icon
          const SizedBox(
            width: 24,
            height: 24,
            child: Icon(
              Icons.cancel_outlined,
              size: 20,
              color: Color(0xFFD33636), // Foundation/Red/Red-500
            ),
          ),

          const SizedBox(width: 16),

          // Delete account text
          Expanded(
            child: Text(
              'Delete account',
              style: titleMedium.copyWith(
                color: const Color(0xFFD33636), // Foundation/Red/Red-600
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // Arrow right icon
          const SizedBox(
            width: 24,
            height: 24,
            child: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFFD33636), // Foundation/Red/Red-500
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';

class LanguageOptionItem extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback? onTap;

  const LanguageOptionItem({
    super.key,
    required this.title,
    required this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Title
            Expanded(
              child: Text(
                title,
                style: titleMedium.copyWith(
                  color: themeData.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // Radio Button
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected 
                      ? themeData.primaryGreen500 
                      : themeData.neutral200,
                  width: isSelected ? 2 : 1,
                ),
                color: isSelected 
                    ? Colors.transparent 
                    : themeData.neutral100,
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: themeData.primaryGreen500,
                        ),
                      ),
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }
}

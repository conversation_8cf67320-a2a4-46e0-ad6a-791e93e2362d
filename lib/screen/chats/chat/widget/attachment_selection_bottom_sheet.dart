import 'package:flutter/material.dart';
import 'package:toii_mesh/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';

class AttachmentSelectionBottomSheet extends StatelessWidget {
  final VoidCallback onGalleryTap;
  final VoidCallback onCameraTap;

  const AttachmentSelectionBottomSheet({
    super.key,
    required this.onGalleryTap,
    required this.onCameraTap,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Send from Gallery option
        _buildAttachmentOption(
          context: context,
          icon: Icon(
            Icons.photo_library,
            size: 24,
            color: themeData.textSecondary,
          ),
          title: 'Send from Gallery',
          onTap: () {
            Navigator.pop(context);
            onGalleryTap();
          },
        ),

        // Take Photo option
        _buildAttachmentOption(
          context: context,
          icon: Icon(
            Icons.camera_alt,
            size: 24,
            color: themeData.textSecondary,
          ),
          title: 'Take Photo',
          onTap: () {
            Navigator.pop(context);
            onCameraTap();
          },
        ),
      ],
    );
  }

  Widget _buildAttachmentOption({
    required BuildContext context,
    required Widget icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final themeData = Theme.of(context);
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            icon,
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: titleMedium.copyWith(
                  color: themeData.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Helper function to show the attachment selection bottom sheet
  static Future<void> show({
    required BuildContext context,
    required VoidCallback onGalleryTap,
    required VoidCallback onCameraTap,
  }) {
    return showTtBottomSheet(
      context,
      child: AttachmentSelectionBottomSheet(
        onGalleryTap: onGalleryTap,
        onCameraTap: onCameraTap,
      ),
    );
  }
}

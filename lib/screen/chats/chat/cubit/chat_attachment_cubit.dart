import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:toii_mesh/core/repository/user_repository.dart';
import 'package:toii_mesh/model/base/base_response.dart';
import 'package:toii_mesh/model/media/media_model.dart';

part 'chat_attachment_state.dart';

class ChatAttachmentCubit extends Cubit<ChatAttachmentState> {
  ChatAttachmentCubit() : super(const ChatAttachmentState());

  final ImagePicker _imagePicker = ImagePicker();
  final UserRepository _userRepository = GetIt.instance<UserRepository>();

  /// Pick image from gallery for chat attachment
  Future<void> pickImageFromGallery() async {
    try {
      emit(state.copyWith(status: ChatAttachmentStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        // Update local image path and automatically upload
        emit(state.copyWith(localImagePath: image.path, errorMessage: null));
        await _uploadImage(image);
      } else {
        emit(
          state.copyWith(
            status: ChatAttachmentStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: ChatAttachmentStatus.failure,
          errorMessage: 'Error selecting image: ${e.toString()}',
        ),
      );
    }
  }

  /// Pick image from camera for chat attachment
  Future<void> pickImageFromCamera() async {
    try {
      emit(state.copyWith(status: ChatAttachmentStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        // Update local image path and automatically upload
        emit(state.copyWith(localImagePath: image.path, errorMessage: null));
        await _uploadImage(image);
      } else {
        emit(
          state.copyWith(
            status: ChatAttachmentStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: ChatAttachmentStatus.failure,
          errorMessage: 'Error taking photo: ${e.toString()}',
        ),
      );
    }
  }

  /// Upload image to server and get CDN URL
  Future<void> _uploadImage(XFile image) async {
    try {
      emit(
        state.copyWith(
          status: ChatAttachmentStatus.uploading,
          uploadProgress: 0.0,
        ),
      );

      final file = File(image.path);
      final BaseResponse<MediaUploadResponseModel> result =
          await _userRepository.uploadMedia(file, 'image');

      if (result.data.cdnUrl != null) {
        emit(
          state.copyWith(
            status: ChatAttachmentStatus.success,
            cdnUrl: result.data.cdnUrl!,
            uploadProgress: 1.0,
            errorMessage: null,
          ),
        );
      } else {
        throw Exception('CDN URL not received from server');
      }
    } catch (e) {
      final errorMessage = 'Failed to upload image: ${e.toString()}';

      emit(
        state.copyWith(
          status: ChatAttachmentStatus.failure,
          errorMessage: errorMessage,
          uploadProgress: 0.0,
        ),
      );
    }
  }

  /// Reset state to initial
  void resetState() {
    emit(const ChatAttachmentState());
  }

  /// Clear current attachment
  void clearAttachment() {
    emit(const ChatAttachmentState());
  }
}

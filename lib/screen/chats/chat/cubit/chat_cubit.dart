import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/model/chat/chat_models.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart'
    hide MessageDeliveryStatus;
import 'package:uuid/uuid.dart';

import 'chat_state.dart';

class ChatCubit extends Cubit<ChatState> {
  final XmtpCubit _xmtpCubit;
  final String _currentUserInboxId;
  StreamSubscription<XmtpMessage>? _messageStreamSubscription;
  Timer? _refreshTimer;

  ChatCubit({
    required XmtpCubit xmtpCubit,
    required String currentUserInboxId,
  })  : _xmtpCubit = xmtpCubit,
        _currentUserInboxId = currentUserInboxId,
        super(const ChatInitial());

  /// Load chat conversation and messages
  Future<void> loadChat(ChatConversation conversation) async {
    try {
      emit(const ChatLoading());

      if (!_xmtpCubit.hasClient) {
        emit(const ChatError(message: 'XMTP client not initialized'));
        return;
      }

      // Load message history
      final messages = await _loadMessages(conversation.id);

      emit(ChatLoaded(conversation: conversation, messages: messages));

      // Start message streaming
      await _startMessageStreaming(conversation.id);

      // Start periodic refresh as backup
      _startPeriodicRefresh(conversation.id);
    } catch (e) {
      log('ChatCubit: Error loading chat: $e');
      emit(ChatError(
        message: 'Failed to load chat: $e',
        conversationId: conversation.id,
      ));
    }
  }

  /// Load messages for a conversation
  Future<List<ChatMessage>> _loadMessages(String conversationId) async {
    try {
      log('ChatCubit: Loading messages for conversation: $conversationId');

      final xmtpPlugin = ToiiXmtpFlutter();
      final xmtpMessages = await xmtpPlugin.getMessages(conversationId);

      log('ChatCubit: Retrieved ${xmtpMessages.length} XMTP messages');

      final chatMessages = <ChatMessage>[];
      for (final msg in xmtpMessages) {
        try {
          if (msg.id.isEmpty) {
            log('ChatCubit: Skipping message with empty ID');
            continue;
          }

          final chatMessage = ChatMessage.fromXmtpMessage(
            msg,
            _currentUserInboxId,
          );
          chatMessages.add(chatMessage);
        } catch (e) {
          log('ChatCubit: Error converting message ${msg.id}: $e');
        }
      }

      // Sort by sent time (newest first for chat UI)
      chatMessages.sort((a, b) => b.sentAt.compareTo(a.sentAt));

      log('ChatCubit: Successfully loaded ${chatMessages.length} chat messages');
      return chatMessages;
    } catch (e) {
      log('ChatCubit: Error loading messages: $e');
      return [];
    }
  }

  /// Send a message
  Future<void> sendMessage(String content) async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    try {
      // Create pending message
      final pendingMessageId = const Uuid().v4();
      final pendingMessage = ChatMessage(
        id: pendingMessageId,
        conversationId: currentState.conversation.id,
        senderInboxId: _currentUserInboxId,
        content: content,
        contentType: 'text/plain',
        sentAt: DateTime.now(),
        deliveryStatus: MessageDeliveryStatus.pending,
        isFromCurrentUser: true,
      );

      // Add pending message to UI
      final updatedMessages = [pendingMessage, ...currentState.messages];
      emit(ChatSendingMessage(
        conversation: currentState.conversation,
        messages: updatedMessages,
        pendingMessageId: pendingMessageId,
        isStreaming: currentState.isStreaming,
      ));

      // Send message via XMTP
      final xmtpPlugin = ToiiXmtpFlutter();
      final sentMessageId = await xmtpPlugin.sendMessage(
        conversationId: currentState.conversation.id,
        content: content,
      );

      // Update message with sent status
      final sentMessage = pendingMessage.copyWith(
        id: sentMessageId,
        deliveryStatus: MessageDeliveryStatus.sent,
      );

      final finalMessages =
          updatedMessages.where((msg) => msg.id != pendingMessageId).toList();
      finalMessages.insert(0, sentMessage);

      emit(ChatMessageSent(
        conversation: currentState.conversation,
        messages: finalMessages,
        sentMessageId: sentMessageId,
        isStreaming: currentState.isStreaming,
      ));
    } catch (e) {
      log('ChatCubit: Error sending message: $e');

      // Handle send error - mark message as failed
      final currentStateAfterError = state;
      if (currentStateAfterError is ChatSendingMessage) {
        final failedMessages = currentStateAfterError.messages.map((msg) {
          if (msg.id == currentStateAfterError.pendingMessageId) {
            return msg.copyWith(deliveryStatus: MessageDeliveryStatus.failed);
          }
          return msg;
        }).toList();

        emit(ChatLoaded(
          conversation: currentStateAfterError.conversation,
          messages: failedMessages,
          isStreaming: currentStateAfterError.isStreaming,
        ));
      }

      emit(ChatError(
        message: 'Failed to send message: $e',
        conversationId: currentState.conversation.id,
      ));
    }
  }

  /// Start message streaming
  Future<void> _startMessageStreaming(String conversationId) async {
    try {
      await _stopMessageStreaming();

      final xmtpPlugin = ToiiXmtpFlutter();
      final messageStream = xmtpPlugin.streamMessages(conversationId);

      _messageStreamSubscription = messageStream.listen(
        (xmtpMessage) => _handleNewMessage(xmtpMessage),
        onError: (error) {
          log('ChatCubit: Message streaming error: $error');
          // Try to restart streaming after a delay on error
          Future.delayed(const Duration(seconds: 5), () {
            if (!isClosed) {
              _startMessageStreaming(conversationId);
            }
          });
        },
        cancelOnError: false,
      );

      log('ChatCubit: Started message streaming for conversation: $conversationId');
    } catch (e) {
      log('ChatCubit: Failed to start message streaming: $e');
    }
  }

  /// Handle new message from stream
  void _handleNewMessage(XmtpMessage xmtpMessage) {
    try {
      final currentState = state;
      if (currentState is! ChatLoaded) return;

      if (xmtpMessage.id.isEmpty) {
        log('ChatCubit: Skipping message with empty ID');
        return;
      }

      // Check if message already exists
      final existingMessage = currentState.messages.any(
        (msg) => msg.id == xmtpMessage.id,
      );
      if (existingMessage) {
        log('ChatCubit: Message already exists: ${xmtpMessage.id}');
        return;
      }

      log('ChatCubit: Processing new message: ${xmtpMessage.id}');

      final newMessage = ChatMessage.fromXmtpMessage(
        xmtpMessage,
        _currentUserInboxId,
      );

      // Remove any pending messages if this is from current user
      List<ChatMessage> updatedMessages = [...currentState.messages];
      if (xmtpMessage.senderInboxId == _currentUserInboxId) {
        // Remove pending messages with same content type (for attachments)
        if (newMessage.contentType == 'xmtp.org/multiRemoteStaticContent:1.0') {
          updatedMessages = updatedMessages
              .where((msg) =>
                  msg.deliveryStatus != MessageDeliveryStatus.pending ||
                  msg.contentType != 'xmtp.org/multiRemoteStaticContent:1.0')
              .toList();
        }
        log('ChatCubit: Processing own message: ${xmtpMessage.id}');
      }

      // Insert new message at the beginning (newest first)
      updatedMessages = [newMessage, ...updatedMessages];

      emit(ChatMessageReceived(
        conversation: currentState.conversation,
        messages: updatedMessages,
        newMessage: newMessage,
        isStreaming: currentState.isStreaming,
      ));

      log('ChatCubit: New message added to chat: ${newMessage.content}');
    } catch (e) {
      log('ChatCubit: Error handling new message: $e');
    }
  }

  /// Start periodic refresh as backup for streaming
  void _startPeriodicRefresh(String conversationId) {
    _stopPeriodicRefresh();

    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (isClosed) {
        timer.cancel();
        return;
      }

      final currentState = state;
      if (currentState is ChatLoaded &&
          currentState.conversation.id == conversationId) {
        try {
          final latestMessages = await _loadMessages(conversationId);

          if (latestMessages.length > currentState.messages.length) {
            log('ChatCubit: Found new messages via refresh');
            emit(currentState.copyWith(messages: latestMessages));
          }
        } catch (e) {
          log('ChatCubit: Error during periodic refresh: $e');
        }
      }
    });
  }

  /// Stop periodic refresh
  void _stopPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Stop message streaming
  Future<void> _stopMessageStreaming() async {
    await _messageStreamSubscription?.cancel();
    _messageStreamSubscription = null;
  }

  /// Refresh messages
  Future<void> refreshMessages() async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    try {
      final messages = await _loadMessages(currentState.conversation.id);
      emit(currentState.copyWith(messages: messages));
    } catch (e) {
      emit(ChatError(
        message: 'Failed to refresh messages: $e',
        conversationId: currentState.conversation.id,
      ));
    }
  }

  /// Send multiple remote attachments
  Future<void> sendMultipleRemoteAttachments(
      List<String> attachmentUrls) async {
    final currentState = state;
    if (currentState is! ChatLoaded) return;

    String? pendingMessageId;
    try {
      // Create pending message for attachments
      pendingMessageId = const Uuid().v4();
      final pendingMessage = ChatMessage(
        id: pendingMessageId,
        conversationId: currentState.conversation.id,
        senderInboxId: _currentUserInboxId,
        content: 'Sending ${attachmentUrls.length} images...',
        contentType: 'xmtp.org/multiRemoteStaticContent:1.0',
        sentAt: DateTime.now(),
        deliveryStatus: MessageDeliveryStatus.pending,
        isFromCurrentUser: true,
        attachments: attachmentUrls
            .map((url) => DecryptedLocalAttachment(
                  fileUri: url,
                  filename: 'image.jpg',
                  mimeType: 'image/jpeg',
                ))
            .toList(),
      );

      // Add pending message to UI
      final updatedMessages = [pendingMessage, ...currentState.messages];
      emit(ChatSendingMessage(
        conversation: currentState.conversation,
        messages: updatedMessages,
        pendingMessageId: pendingMessageId,
        isStreaming: currentState.isStreaming,
      ));

      // Send the attachments with timeout
      final sendFuture = _xmtpCubit.sendMultipleRemoteAttachments(
        conversationId: currentState.conversation.id,
        attachmentUrls: attachmentUrls,
      );

      // Wait for send completion or timeout
      await sendFuture.timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('Send timeout after 30 seconds');
        },
      );

      log('ChatCubit: Attachments sent successfully, waiting for stream confirmation...');

      // Wait a bit for the stream message to arrive, then fallback if needed
      await Future.delayed(const Duration(seconds: 3));

      // Check if the real message arrived via stream
      final currentStateAfterSend = state;
      if (currentStateAfterSend is ChatLoaded) {
        final hasRealMessage = currentStateAfterSend.messages.any((msg) =>
            msg.id != pendingMessageId &&
            msg.contentType == 'xmtp.org/multiRemoteStaticContent:1.0' &&
            msg.isFromCurrentUser &&
            msg.attachments?.isNotEmpty == true);

        if (!hasRealMessage) {
          // Stream message didn't arrive, convert pending to sent
          log('ChatCubit: Stream message not received, converting pending to sent');
          final sentMessage = pendingMessage.copyWith(
            deliveryStatus: MessageDeliveryStatus.sent,
          );

          final updatedMessagesAfterSend = currentStateAfterSend.messages
              .map((msg) => msg.id == pendingMessageId ? sentMessage : msg)
              .toList();

          emit(ChatLoaded(
            conversation: currentState.conversation,
            messages: updatedMessagesAfterSend,
            isStreaming: currentStateAfterSend.isStreaming,
          ));
        } else {
          // Real message arrived, remove pending
          final messagesWithoutPending = currentStateAfterSend.messages
              .where((msg) => msg.id != pendingMessageId)
              .toList();

          emit(ChatLoaded(
            conversation: currentState.conversation,
            messages: messagesWithoutPending,
            isStreaming: currentStateAfterSend.isStreaming,
          ));
        }
      }
    } catch (e) {
      log('ChatCubit: Error sending attachments: $e');

      // Handle error - remove pending message and show error
      final currentStateOnError = state;
      if (currentStateOnError is ChatLoaded) {
        final messagesWithoutPending = currentStateOnError.messages
            .where((msg) => msg.id != pendingMessageId)
            .toList();

        emit(ChatLoaded(
          conversation: currentState.conversation,
          messages: messagesWithoutPending,
          isStreaming: currentStateOnError.isStreaming,
        ));
      }

      emit(ChatError(
        message: 'Failed to send attachments: ${e.toString()}',
        conversationId: currentState.conversation.id,
      ));
    }
  }

  @override
  Future<void> close() async {
    await _stopMessageStreaming();
    _stopPeriodicRefresh();
    return super.close();
  }
}

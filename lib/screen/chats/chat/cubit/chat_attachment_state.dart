part of 'chat_attachment_cubit.dart';

enum ChatAttachmentStatus { initial, loading, uploading, success, failure }

extension ChatAttachmentStatusX on ChatAttachmentStatus {
  bool get isInitial => this == ChatAttachmentStatus.initial;
  bool get isLoading => this == ChatAttachmentStatus.loading;
  bool get isUploading => this == ChatAttachmentStatus.uploading;
  bool get isSuccess => this == ChatAttachmentStatus.success;
  bool get isFailure => this == ChatAttachmentStatus.failure;
}

final class ChatAttachmentState extends Equatable {
  final ChatAttachmentStatus status;
  final String? errorMessage;
  final String? localImagePath;
  final String? cdnUrl;
  final double uploadProgress;

  const ChatAttachmentState({
    this.status = ChatAttachmentStatus.initial,
    this.errorMessage,
    this.localImagePath,
    this.cdnUrl,
    this.uploadProgress = 0.0,
  });

  ChatAttachmentState copyWith({
    ChatAttachmentStatus? status,
    String? errorMessage,
    String? localImagePath,
    String? cdnUrl,
    double? uploadProgress,
  }) {
    return ChatAttachmentState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      localImagePath: localImagePath ?? this.localImagePath,
      cdnUrl: cdnUrl ?? this.cdnUrl,
      uploadProgress: uploadProgress ?? this.uploadProgress,
    );
  }

  @override
  List<Object?> get props => [
        status,
        errorMessage,
        localImagePath,
        cdnUrl,
        uploadProgress,
      ];
}

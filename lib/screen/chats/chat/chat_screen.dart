import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart' hide ChatState;
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/model/chat/chat_models.dart';
import 'package:toii_mesh/screen/chats/chat/cubit/chat_attachment_cubit.dart';
import 'package:toii_mesh/screen/chats/chat/cubit/chat_cubit.dart';
import 'package:toii_mesh/screen/chats/chat/cubit/chat_state.dart';
import 'package:toii_mesh/screen/chats/chat/widget/attachment_selection_bottom_sheet.dart';

class ChatScreen extends StatelessWidget {
  final ChatConversation conversation;
  final String currentUserInboxId;
  final XmtpCubit xmtpCubit;

  const ChatScreen({
    super.key,
    required this.conversation,
    required this.currentUserInboxId,
    required this.xmtpCubit,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ChatCubit(
            xmtpCubit: xmtpCubit,
            currentUserInboxId: currentUserInboxId,
          )..loadChat(conversation),
        ),
        BlocProvider(
          create: (context) => ChatAttachmentCubit(),
        ),
      ],
      child: ChatView(
        conversation: conversation,
        currentUserInboxId: currentUserInboxId,
      ),
    );
  }
}

class ChatView extends StatelessWidget {
  final ChatConversation conversation;
  final String currentUserInboxId;

  const ChatView({
    super.key,
    required this.conversation,
    required this.currentUserInboxId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              conversation.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            BlocBuilder<ChatCubit, ChatState>(
              builder: (context, state) {
                String subtitle = '';
                if (state is ChatLoading) {
                  subtitle = 'Loading...';
                } else if (state is ChatLoaded) {
                  if (conversation.type.toString().contains('dm')) {
                    subtitle = 'Direct Message';
                  } else {
                    subtitle = 'Group • ${state.messages.length} messages';
                  }
                } else if (state is ChatError) {
                  subtitle = 'Error';
                }

                return Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.normal,
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.attach_file, color: Colors.black),
            onPressed: () {
              _showAttachmentOptions(context);
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.black),
            onPressed: () {
              // Clear image cache and refresh messages
              imageCache.clear();
              context.read<ChatCubit>().refreshMessages();
            },
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ChatCubit, ChatState>(
            listener: (context, state) {
              if (state is ChatError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                    action: SnackBarAction(
                      label: 'Retry',
                      textColor: Colors.white,
                      onPressed: () {
                        context.read<ChatCubit>().loadChat(conversation);
                      },
                    ),
                  ),
                );
              }
            },
          ),
          BlocListener<ChatAttachmentCubit, ChatAttachmentState>(
            listener: (context, state) {
              if (state.status.isSuccess && state.cdnUrl != null) {
                // Send the uploaded image using the CDN URL
                context
                    .read<ChatCubit>()
                    .sendMultipleRemoteAttachments([state.cdnUrl!]);
                // Reset the attachment cubit state
                context.read<ChatAttachmentCubit>().resetState();
              } else if (state.status.isFailure) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content:
                        Text(state.errorMessage ?? 'Failed to upload image'),
                    backgroundColor: Colors.red,
                  ),
                );
                // Reset the attachment cubit state
                context.read<ChatAttachmentCubit>().resetState();
              }
            },
          ),
        ],
        child: BlocBuilder<ChatCubit, ChatState>(
          builder: (context, state) {
            if (state is ChatLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading messages...'),
                  ],
                ),
              );
            }

            if (state is ChatError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline,
                        size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to load chat',
                      style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[500]),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        context.read<ChatCubit>().loadChat(conversation);
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            if (state is ChatLoaded) {
              return _buildChatUI(context, state);
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildChatUI(BuildContext context, ChatLoaded state) {
    final user = types.User(id: currentUserInboxId, firstName: 'You');

    final messages = state.messages
        .map((chatMessage) => chatMessage.toFlutterChatMessage())
        .toList();

    return Chat(
      messages: messages,
      onSendPressed: (types.PartialText message) {
        context.read<ChatCubit>().sendMessage(message.text);
      },
      user: user,
      theme: const DefaultChatTheme(
        primaryColor: Color(0xFF6C4EFF),
        secondaryColor: Color(0xFFF5F5F5),
        backgroundColor: Colors.white,
        inputBackgroundColor: Color(0xFFF5F5F5),
        inputTextColor: Colors.black87,
        messageBorderRadius: 16,
        messageInsetsHorizontal: 12,
        messageInsetsVertical: 8,
      ),
      showUserAvatars: conversation.type.toString().contains('group'),
      showUserNames: conversation.type.toString().contains('group'),
      emptyState: _buildEmptyState(),
      customBottomWidget:
          state is ChatSendingMessage ? _buildSendingIndicator() : null,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            conversation.type.toString().contains('dm')
                ? Icons.person_outline
                : Icons.group_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            conversation.type.toString().contains('dm')
                ? 'Start your conversation'
                : 'Welcome to the group',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Send a message to get started',
            style: TextStyle(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  void _showAttachmentOptions(BuildContext context) {
    final attachmentCubit = context.read<ChatAttachmentCubit>();

    AttachmentSelectionBottomSheet.show(
      context: context,
      onGalleryTap: () => attachmentCubit.pickImageFromGallery(),
      onCameraTap: () => attachmentCubit.pickImageFromCamera(),
    );
  }

  Widget _buildSendingIndicator() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 8),
          Text(
            'Sending...',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
        ],
      ),
    );
  }
}

import Flutter
import UIKit
import XMTPiOS
import OSLog
import Swift<PERSON>
import CryptoKit
import web3swift

@available(iOS 17.0, *)

@Observable
class ObservableCache<T> {
    private var itemCache = NSCache<NSString, ObservableItem<T>>()
    private let lock = NSLock()
    private let defaultValue: (String) -> T?
    var loader: ((String) async throws -> T)?

    init(defaultValue: T? = nil, loader: ((String) async throws -> T)? = nil) {
        self.loader = loader
        self.defaultValue = {_ in defaultValue }
    }

    init(defaultValue: @escaping (String) -> T? = {_ in nil }, loader: ((String) async throws -> T)? = nil) {
        self.loader = loader
        self.defaultValue = defaultValue
    }

    // This lets callers get the same @Observable item so that any updates are broadcast.
    subscript(identifier: String) -> ObservableItem<T> {
        let result = getOrCreate(identifier: identifier)
        if !result.fromCache {
            _ = doLoad(identifier: identifier, item: result.item)
        }
        return result.item
    }

    // Empty the cache
    func clear() {
        lock.lock()
        itemCache.removeAllObjects()
        lock.unlock()
    }

    // Permit outside insertion of values (e.g. prefill or update the cache)
    // Note: listeners will be broadcast with the update.
    func insert(identifier: String, value: T) {
        let result = getOrCreate(identifier: identifier)
        result.item.value = value
    }

    // Permit outside initiation of a reload of the value for an item.
    func reload(_ identifier: String) -> Task<T?, Error> {
        let result = getOrCreate(identifier: identifier)
        return doLoad(identifier: identifier, item: result.item)
    }

    // This locks to avoid duplicate entries for the specified identifier.
    private func getOrCreate(identifier: String) -> (item: ObservableItem<T>, fromCache: Bool) {
        lock.lock()
        let key = identifier as NSString
        if let cached = itemCache.object(forKey: key) {
            lock.unlock()
            return (cached, true)
        }
        let item = ObservableItem<T>(identifier: identifier, defaultValue: defaultValue(identifier))
        itemCache.setObject(item, forKey: key)
        lock.unlock()
        return (item, false)
    }

    private func doLoad(identifier: String, item: ObservableItem<T>) -> Task<T?, Error> {
        return Task {
            do {
                item.value = try await loader?(identifier)
                return item.value
            } catch {
                print("load error (identifier = \"\(identifier)\"): \(error)")
                return nil
            }
        }
    }
}

@available(iOS 17.0, *)

@Observable
class ObservableItem<T> {
    let identifier: String
    var value: T?

    init(identifier: String, defaultValue: T? = nil) {
        self.identifier = identifier
        self.value = defaultValue
    }
}

@available(iOS 17.0, *)

public class ToiiXmtpFlutterPlugin: NSObject, FlutterPlugin {
    
    enum State {
        case loading
        case loggedOut
        case loggedIn
    }
    enum XmtpSessionError: Error {
        case notInitialized
        case unableToLoadData
    }
    
    private(set) var state: State = .loading
    var inboxId: String? {
        client?.inboxID
    }
    
    private var client: Client?
    private(set) var conversationIds: [String] = []
        let conversations = ObservableCache<Conversation>(defaultValue: nil)
        let conversationMembers = ObservableCache<[Member]>(defaultValue: [])
        let conversationMessages = ObservableCache<[DecodedMessage]>(defaultValue: [])
        let inboxes = ObservableCache<InboxState>(defaultValue: nil)
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "toii_xmtp_flutter", binaryMessenger: registrar.messenger())
        let instance = ToiiXmtpFlutterPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult)  {
        switch call.method {
        case "getPlatformVersion":
            result("iOS " + UIDevice.current.systemVersion)
        case "createClient":
            createClient(call: call, result: result)
//            if let arg = call.arguments as? [String: Any], let privateKey = arg["privateKey"] as? String {
//                Task {
//                    try await createClient(privateKey: privateKey, result: result)
//                }
//            }
        case "listConversations":
            guard let args = call.arguments as? [String: Any],
                  let client = self.client else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "Client not initialized", details: nil))
                return
            }

            let includeGroups = args["includeGroups"] as? Bool ?? true
            let includeDms = args["includeDms"] as? Bool ?? true

            Task {
                var conversations: [[String: Any?]] = []

                // Sync trước (nếu có API)
                do {
                    try await client.conversations.sync()
                    print("✅ Conversations synced successfully")
                } catch {
                    print("⚠️ Failed to sync conversations: \(error.localizedDescription)")
                    // vẫn tiếp tục với cache
                }

                // Add DMs
                if includeDms {
                    do {
                        let dms = try await client.conversations.listDms()
                        print("📩 Found \(dms.count) DMs")

                        for dm in dms {
                            let dmData: [String: Any?] = [
                                "id": dm.id,
                                "topic": dm.topic,
                                "peerInboxId": try dm.peerInboxId,
                                "createdAt": Int(dm.createdAt.timeIntervalSince1970 * 1000), // ms
                                "consentState": try? dm.consentState().rawValue.lowercased(),
                                "isActive": try dm.isActive(),
                                "type": "dm",
                                "peerAddress": NSNull() // v3 dùng inboxId, không có peerAddress
                            ]
                            conversations.append(dmData)
                        }
                    } catch {
                        print("❌ Error listing DMs: \(error.localizedDescription)")
                    }
                }

                // Add Groups
                if includeGroups {
                    do {
                        let groups = try await client.conversations.listGroups()
                        print("👥 Found \(groups.count) groups")

                        for group in groups {
                            let members = try await group.members
                            let groupData: [String: Any?] = [
                                "id": group.id,
                                "topic": group.topic,
                                "name": group.name ?? "",
                                "description": group.description ?? "",
                                "imageUrl": group.imageUrl ?? "",
                                "createdAt": Int(group.createdAt.timeIntervalSince1970 * 1000),
                                "consentState": try? group.consentState().rawValue.lowercased(),
                                "isActive": try group.isActive(),
                                "type": "group",
                                "memberCount": members.count,
                                "memberInboxIds": members.map { $0.inboxId },
                                "adminInboxIds": try? group.listAdmins(),
                                "superAdminInboxIds": try? group.listSuperAdmins(),
                                "creatorInboxId": try? group.addedByInboxId()
                            ]
                            conversations.append(groupData)
                        }
                    } catch {
                        print("❌ Error listing groups: \(error.localizedDescription)")
                    }
                }

                print("📤 Returning \(conversations.count) total conversations")
                result(conversations)
            }
        case "getInboxId":
            result(client?.inboxID ?? "")
        case "findOrCreateDm":
            guard let args = call.arguments as? [String: Any],
                  let targetInboxId = args["targetInboxId"] as? String,
                  let client = self.client else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "targetInboxId is required or client not ready", details: nil))
                return
            }

            Task {
                do {
                    // iOS XMTP SDK v3 có API tương ứng
                    let dm = try await client.conversations.findOrCreateDm(with: targetInboxId)

                    let dmData: [String: Any] = [
                        "id": dm.id,
                        "topic": dm.topic,
                        "peerInboxId": try? dm.peerInboxId,
                        "createdAt": Int(dm.createdAt.timeIntervalSince1970 * 1000), // ms cho giống Android
                        "consentState": try dm.consentState().rawValue.lowercased(),
                        "isActive": true,
                        "peerAddress": NSNull() // XMTP v3 DMs dùng inbox ID nên để null
                    ]

                    print("✅ DM created/found successfully: \(dm.id)")
                    result(dmData)
                } catch {
                    print("❌ Failed to find or create DM: \(error.localizedDescription)")
                    result(FlutterError(code: "FIND_OR_CREATE_DM_ERROR", message: "Failed to find or create DM: \(error.localizedDescription)", details: nil))
                }
            }
        case "sendMessage":
            guard let args = call.arguments as? [String: Any],
                  let client = self.client else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "Client not initialized", details: nil))
                return
            }

            guard let conversationId = args["conversationId"] as? String else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "conversationId is required", details: nil))
                return
            }

            guard let content = args["content"] as? String else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "content is required", details: nil))
                return
            }

            print("📤 Sending message to conversation: \(conversationId)")

            Task {
                do {
                    // Tìm conversation
                    guard let conversation = try await client.conversations.findConversation(conversationId: conversationId) else {
                        print("❌ Conversation not found: \(conversationId)")
                        result(FlutterError(code: "CONVERSATION_NOT_FOUND", message: "Conversation not found: \(conversationId)", details: nil))
                        return
                    }
            
                    print("🔄 Syncing conversation before sending message: \(conversationId)")
                    try await conversation.sync()
                    guard let messageId = try? await conversation.send(text: content) else {
                        print("❌ Failed to send message:")
                        result(FlutterError(code: "SEND_MESSAGE_ERROR", message: "Failed to send message:", details: nil))
                        return
                    }
                    print("✅ Message sent successfully with ID: \(messageId ?? "unknown")")
                    result("messageId")
                } catch {
                    print("❌ Failed to send message: \(error.localizedDescription)")
                    result(FlutterError(code: "SEND_MESSAGE_ERROR", message: "Failed to send message: \(error.localizedDescription)", details: nil))
                }
            }

        case "getMessages":
            guard let args = call.arguments as? [String: Any],
                  let client = self.client else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "Client not initialized", details: nil))
                return
            }

            guard let conversationId = args["conversationId"] as? String else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "conversationId is required", details: nil))
                return
            }

            print("📩 Getting messages for conversation: \(conversationId)")

            Task {
                do {
                    guard let conversation = try await client.conversations.findConversation(conversationId: conversationId) else {
                        print("❌ Conversation not found: \(conversationId)")
                        result(FlutterError(code: "CONVERSATION_NOT_FOUND", message: "Conversation not found: \(conversationId)", details: nil))
                        return
                    }
            
                    print("🔄 Syncing conversation before sending message: \(conversationId)")
                    try await conversation.sync()

                    let limit = args["limit"] as? Int ?? 50
                    let beforeNs = args["beforeNs"] as? Int64
                    let afterNs = args["afterNs"] as? Int64

                    print("🔎 Retrieving messages with limit: \(limit)")

                    var messages: [DecodedMessage] = []
                   // if let dm = conversation as? Dm {
                    messages = try await conversation.messages(limit: limit, beforeNs: beforeNs, afterNs: afterNs)
//                    } else if let group = conversation as? Group {
//                        messages = try await group.messages(limit: limit, beforeNs: beforeNs, afterNs: afterNs)
//                    } else {
//                        result(FlutterError(code: "UNSUPPORTED_CONVERSATION_TYPE", message: "Unsupported conversation type", details: nil))
//                        return
//                    }

                    let messageList: [[String: Any?]] = messages.compactMap { message in
                        do {
                            guard !message.id.isEmpty else {
                                    print("⚠️ Skipping message with empty ID")
                                return [:]   // ❌ Swift báo lỗi vì nil không khớp với kiểu [String: Any?]
                                }

                            let messageBody = try? message.body ?? ""
                            var contentType = "text/plain"
                            var content = messageBody
                            
                            // Check multiple attachment format
//                            if messageBody.hasPrefix("XMTP_MULTI_ATTACHMENT:") {
//                                contentType = "xmtp.org/multiRemoteStaticContent:1.0"
//                                content = String(messageBody.dropFirst("XMTP_MULTI_ATTACHMENT:".count))
//                                print("📎 Multiple remote attachment detected: \(content)")
//                            } else {
//                                if let encodedType = message.encodedContent.type.typeId {
                                   // contentType = encodedType
                           //     }
                        //    }

                            return [
                                "id": message.id,
                                "conversationId": conversationId,
                                "senderInboxId": message.senderInboxId ?? "",
                                "content": content,
                                "contentType": try? message.encodedContent.type,
                                "sentAt": message.sentAt.timeIntervalSince1970 * 1000, // ms
                                "deliveryStatus": {
                                    switch message.deliveryStatus {
                                    case .published: return "published"
                                    case .unpublished: return "unpublished"
                                    case .failed: return "failed"
                                    default: return "published"
                                    }
                                }()
                            ]
                        } catch {
                            print("⚠️ Error converting message: \(error.localizedDescription)")
                            return nil
                        }
                    }

                    print("✅ Retrieved \(messageList.count) messages")
                    result(messageList)
                } catch {
                    print("❌ Error getting messages: \(error.localizedDescription)")
                    result(FlutterError(code: "GET_MESSAGES_ERROR", message: "Failed to get messages: \(error.localizedDescription)", details: nil))
                }
            }
        case  "syncConversations":
            Task {
                   do {
                       guard let client = self.client else {
                           result(FlutterError(code: "NO_CLIENT",
                                               message: "Client is not initialized",
                                               details: nil))
                           return
                       }

                       print("🔄 Syncing conversations...")
                       try await client.conversations.sync()
                       print("✅ Conversations synced successfully")

                       result(true)
                   } catch {
                       print("❌ Failed to sync conversations: \(error.localizedDescription)")
                       result(FlutterError(code: "SYNC_CONVERSATIONS_ERROR",
                                           message: "Failed to sync conversations: \(error.localizedDescription)",
                                           details: nil))
                   }
               }
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    func createClient(call: FlutterMethodCall, result: @escaping FlutterResult) {
        Task {
            do {
                guard let args = call.arguments as? [String: Any],
                      let privateKey = args["privateKey"] as? String else {
                    throw NSError(domain: "XMTP", code: 400, userInfo: [NSLocalizedDescriptionKey: "privateKey is required"])
                }
                
                let environment = (args["environment"] as? String)?.lowercased() ?? "dev"
                let appVersion = args["appVersion"] as? String
                let dbDirectory = args["dbDirectory"] as? String
                let historySyncUrl = args["historySyncUrl"] as? String
                let deviceSyncEnabled = args["deviceSyncEnabled"] as? Bool ?? true
                let debugEventsEnabled = args["debugEventsEnabled"] as? Bool ?? false
                let privateKeyData = Data(hex: privateKey)
                // let account = try PrivateKey(privateKeyData)
                
                // To re-use a randomly generated account during dev,
                // copy these from the logs of the first run:
                let wallet = try PrivateKey(privateKeyData)
                
                // 👉 Choose environment
                let xmtpEnv: XMTPEnvironment
                switch environment {
                case "production": xmtpEnv = .production
                case "local": xmtpEnv = .local
                default: xmtpEnv = .dev
                }
                let key = try secureRandomBytes(count: 32)
                // 👉 Optional: DB encryption key
          //      let dbEncryptionKey = generateDbEncryptionKey(from: privateKey)
                
                // 👉 Client options
                let options = ClientOptions(
                    api: .init(env: xmtpEnv, isSecure: true),
                    dbEncryptionKey: key,
                    //enableDeviceSync: deviceSyncEnabled,
                //    enableDebugEvents: debugEventsEnabled
                )
                
                // 👉 Create client
                self.client = try await Client.create(account: wallet, options: options)
                
                print("✅ Client created successfully")
               // print("InboxId: \(self.client!.inboxId)")
             //   print("InstallationId: \(self.client!.installationId)")
                print("Environment: \(environment)")
             //   print("Address: \(wallet.address)")
                print("DbPath: \(self.client!.dbPath ?? "nil")")
                
                let clientData: [String: Any?] = [
                    "inboxId": self.client?.inboxID ?? "",
                    "installationId": self.client?.installationID ?? "",
                    "environment": environment,
                    "dbPath": self.client?.dbPath,
                    "publicIdentity": [
                        "kind": "ethereum",
                        "identifier": wallet.publicKey
                    ]
                ]
                
                result(clientData)
            } catch {
                print("❌ Failed to create client: \(error.localizedDescription)")
                result(FlutterError(code: "CLIENT_CREATION_ERROR",
                                    message: "Failed to create client: \(error.localizedDescription)",
                                    details: nil))
            }
        }
    }
    
//    func createClient(privateKey: String, result: @escaping FlutterResult) async throws {
//        print("login")
//        //guard state == .loggedOut else { return }
//        state = .loading
//        
//        // TODO: accept as params
//        // TODO: use real account
//        //  let account = try PrivateKey.generate()
//        let dbKey = Data((0 ..< 32)
//            .map { _ in UInt8.random(in: UInt8.min ... UInt8.max) })
//        let privateKeyData = Data(hex: privateKey)
//        // let account = try PrivateKey(privateKeyData)
//        
//        // To re-use a randomly generated account during dev,
//        // copy these from the logs of the first run:
//        let account = try PrivateKey(privateKeyData)
//        //      let dbKey = Data(base64Encoded: "...")
//        print("dbKey: \(dbKey.base64EncodedString())")
//        print("account: \((try? account.jsonString()) ?? "")")
//        
//        client = try await Client.create(account: account, options: ClientOptions(dbEncryptionKey: dbKey))
//        let value = ["inboxId": self.client?.inboxID ?? "", "installationId": self.client?.installationID ?? "", "environment": "dev", "dbPath": self.client?.dbPath ?? ""] as [String : String]
//        
//        result(value)
//    }
//    
    
   func initData() {
        conversations.loader = { conversationId in
                 guard let client = self.client else {
                     throw XmtpSessionError.notInitialized
                 }
                 if let c = try await client.conversations.findConversation(conversationId: conversationId) {
                     return c
                 }
                 throw XmtpSessionError.unableToLoadData
             }
             conversationMembers.loader = { conversationId in
                 guard let client = self.client else {
                     return []
                 }
                 if let c = try await client.conversations.findConversation(conversationId: conversationId) {
                     return try await c.members()
                 }
                 return []
             }
             conversationMessages.loader = { conversationId in
                 guard let client = self.client else {
                     return []
                 }
                 if let c = try await client.conversations.findConversation(conversationId: conversationId) {
                     return try await c.messages(limit: 10) // TODO paging etc.
                 }
                 return []
             }
             inboxes.loader = { inboxId in
                 guard let client = self.client else {
                     throw XmtpSessionError.notInitialized
                 }
                 if let inbox = try await client.inboxStatesForInboxIds(
                     refreshFromNetwork: true, // TODO: consider false sometimes?
                     inboxIds: [inboxId]).first // there's only one.
                 {
                     return inbox
                 }
                 throw XmtpSessionError.unableToLoadData
             }
    }
    
    
    
    func refreshConversations() async throws {
          _ = try await client?.conversations.syncAllConversations()
          let conversations = (try? await client?.conversations.list()) ?? []  // TODO: paging etc.
          self.conversationIds = conversations.map { $0.id }
      }

      func refreshConversation(conversationId: String) async throws {
          guard let c = try await client?.conversations.findConversation(conversationId: conversationId) else {
              return // TODO: consider logging failure instead
          }
          try await c.sync()
          _ = await [
              try conversations.reload(conversationId).result.get(),
              try conversationMessages.reload(conversationId).result.get(),
              try conversationMembers.reload(conversationId).result.get()
          ] as [Any?]
      }

      func sendMessage(_ message: String, to conversationId: String) async throws -> Bool {
          guard let c = try await client?.conversations.findConversation(conversationId: conversationId) else {
              return false // TODO: consider logging failure instead
          }
          guard (try? await c.send(text: message)) != nil else {
              return false
          }
          _ = conversationMessages.reload(conversationId) // TODO: consider try/awaiting the roundtrip here
          return true
      }
    
    
    
    // Async function to load conversations
    func loadConversations(result: @escaping FlutterResult) async {
        Task {
               do {
                   let loadedConversations = try await client?.conversations
//                   let convArray = loadedConversations.map { convo in
//                                 return [
//                                     "id":  convo.topic,
//                                     "peerAddress": convo.peerAddress
//                                 ]
//                             }
////                             result(convArray)
                   result([])
               } catch {
                   result(FlutterError(code: "LIST_ERROR", message: error.localizedDescription, details: nil))
               }
           }
    }

    
}

func secureRandomBytes(count: Int) throws -> Data {
    var bytes = [UInt8](repeating: 0, count: count)

    // Fill bytes with secure random data
    let status = SecRandomCopyBytes(
        kSecRandomDefault,
        count,
        &bytes
    )

    // A status of errSecSuccess indicates success
    if status == errSecSuccess {
        return Data(bytes)
    } else {
        fatalError("could not generate random bytes")
    }
}

package com.toii.ai.toii_xmtp_flutter.handlers

import android.util.Log
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.xmtp.android.library.Client
import org.xmtp.android.library.Dm
import org.xmtp.android.library.Group
import java.security.SecureRandom
import com.google.gson.Gson

class AttachmentHandler {
    
    companion object {
        private const val TAG = "XMTP_AttachmentHandler"

        // Content type identifiers
        private const val MULTIPLE_REMOTE_ATTACHMENT_TYPE = "xmtp.org/multiRemoteStaticContent:1.0"
        private const val REMOTE_ATTACHMENT_TYPE = "xmtp.org/remoteStaticAttachment:1.0"
    }
    
    fun sendMultipleRemoteAttachments(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val conversationId = call.argument<String>("conversationId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "conversationId is required", null)
                val attachmentUrls = call.argument<List<String>>("attachmentUrls")
                    ?: return@launch result.error("INVALID_ARGUMENT", "attachmentUrls is required", null)
                
                Log.d(TAG, "Sending multiple remote attachments to conversation: $conversationId")
                Log.d(TAG, "Attachment URLs: $attachmentUrls")
                
                val conversation = findConversationWithSync(client, conversationId)
                if (conversation == null) {
                    Log.e(TAG, "Conversation not found: $conversationId")
                    return@launch result.error("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId", null)
                }
                
                // For now, create hardcoded multiple remote attachment content as JSON string
                val multipleRemoteAttachmentJson = createHardcodedMultipleRemoteAttachmentJson(attachmentUrls)

                // Send as text message with special content type marker
                // In a real implementation, this would use proper XMTP content type encoding
                val messageContent = "XMTP_MULTI_ATTACHMENT:$multipleRemoteAttachmentJson"

                val messageId = when (conversation) {
                    is Dm -> {
                        Log.d(TAG, "Sending DM multiple remote attachments")
                        conversation.send(messageContent)
                    }
                    is Group -> {
                        Log.d(TAG, "Sending Group multiple remote attachments")
                        conversation.send(messageContent)
                    }
                    else -> {
                        return@launch result.error("UNSUPPORTED_CONVERSATION_TYPE", "Unsupported conversation type", null)
                    }
                }
                
                Log.d(TAG, "Multiple remote attachments sent successfully with ID: $messageId")
                result.success(messageId)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send multiple remote attachments: ${e.message}", e)
                result.error("SEND_ATTACHMENTS_ERROR", "Failed to send multiple remote attachments: ${e.message}", e.toString())
            }
        }
    }
    
    fun decryptMultipleRemoteAttachments(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val attachmentData = call.argument<Map<String, Any>>("attachmentData")
                    ?: return@launch result.error("INVALID_ARGUMENT", "attachmentData is required", null)
                
                Log.d(TAG, "Decrypting multiple remote attachments")
                
                // Parse the attachment data
                val attachments = attachmentData["attachments"] as? List<Map<String, Any>>
                    ?: return@launch result.error("INVALID_ARGUMENT", "attachments list is required", null)
                
                val decryptedAttachments = mutableListOf<Map<String, Any>>()
                
                for (attachment in attachments) {
                    val url = attachment["url"] as? String
                        ?: continue
                    val nonce = attachment["nonce"] as? String
                        ?: continue
                    val secret = attachment["secret"] as? String
                        ?: continue
                    val filename = attachment["filename"] as? String
                        ?: "attachment"
                    val mimeType = attachment["mimeType"] as? String
                        ?: "application/octet-stream"
                    
                    // For hardcoded implementation, just return the URL as fileUri
                    // In real implementation, this would download, decrypt, and save locally
                    val decryptedAttachment = mapOf(
                        "fileUri" to url, // Hardcoded: use URL directly
                        "filename" to filename,
                        "mimeType" to mimeType,
                        "contentLength" to (attachment["contentLength"] as? Int ?: 0)
                    )
                    
                    decryptedAttachments.add(decryptedAttachment)
                }
                
                Log.d(TAG, "Decrypted ${decryptedAttachments.size} attachments")
                result.success(decryptedAttachments)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to decrypt multiple remote attachments: ${e.message}", e)
                result.error("DECRYPT_ATTACHMENTS_ERROR", "Failed to decrypt attachments: ${e.message}", e.toString())
            }
        }
    }
    
    private fun createHardcodedMultipleRemoteAttachmentJson(urls: List<String>): String {
        // Create a simple JSON structure for multiple remote attachments
        // In real implementation, this would use proper XMTP encoding
        val attachments = urls.mapIndexed { index, url ->
            mapOf(
                "url" to url,
                "nonce" to generateRandomNonce(),
                "contentDigest" to generateRandomDigest(),
                "secret" to generateRandomSecret(),
                "salt" to generateRandomSalt(),
                "filename" to "image_$index.jpg",
                "mimeType" to "image/jpeg",
                "contentLength" to 1024000 // 1MB placeholder
            )
        }

        val multipleRemoteAttachment = mapOf(
            "attachments" to attachments,
            "scheme" to "https://"
        )

        // Convert to JSON string
        return Gson().toJson(multipleRemoteAttachment)
    }
    
    private fun generateRandomNonce(): String {
        val bytes = ByteArray(12)
        SecureRandom().nextBytes(bytes)
        return android.util.Base64.encodeToString(bytes, android.util.Base64.NO_WRAP)
    }
    
    private fun generateRandomDigest(): String {
        val bytes = ByteArray(32)
        SecureRandom().nextBytes(bytes)
        return android.util.Base64.encodeToString(bytes, android.util.Base64.NO_WRAP)
    }
    
    private fun generateRandomSecret(): String {
        val bytes = ByteArray(32)
        SecureRandom().nextBytes(bytes)
        return android.util.Base64.encodeToString(bytes, android.util.Base64.NO_WRAP)
    }
    
    private fun generateRandomSalt(): String {
        val bytes = ByteArray(16)
        SecureRandom().nextBytes(bytes)
        return android.util.Base64.encodeToString(bytes, android.util.Base64.NO_WRAP)
    }

    private fun getClientFromCall(call: MethodCall, result: Result): Client? {
        return ClientHandler.getClient() ?: run {
            result.error("NO_CLIENT", "Client not initialized", null)
            null
        }
    }

    private suspend fun findConversationWithSync(client: Client, conversationId: String): Any? {
        Log.d(TAG, "Finding conversation with sync: $conversationId")

        var conversation = findConversationInLists(client, conversationId)
        if (conversation != null) {
            Log.d(TAG, "Found conversation in current lists: $conversationId")
            return conversation
        }

        try {
            Log.d(TAG, "Syncing conversations before retry: $conversationId")
            client.conversations.sync()
            conversation = findConversationInLists(client, conversationId)
            if (conversation != null) {
                Log.d(TAG, "Found conversation after sync: $conversationId")
                return conversation
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing conversations: ${e.message}")
        }

        try {
            Log.d(TAG, "Trying native findConversation method: $conversationId")
            val nativeConversation = client.conversations.findConversation(conversationId)
            if (nativeConversation != null) {
                Log.d(TAG, "Found conversation using native method: $conversationId")
                return when (nativeConversation) {
                    is org.xmtp.android.library.Conversation.Dm -> nativeConversation.dm
                    is org.xmtp.android.library.Conversation.Group -> nativeConversation.group
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error using native findConversation: ${e.message}")
        }

        Log.w(TAG, "Conversation not found after all attempts: $conversationId")
        return null
    }

    private fun findConversationInLists(client: Client, conversationId: String): Any? {
        try {
            val dms = client.conversations.listDms()
            for (dm in dms) {
                if (dm.id == conversationId) {
                    Log.d(TAG, "Found DM: $conversationId")
                    return dm
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error listing DMs: ${e.message}")
        }

        try {
            val groups = client.conversations.listGroups()
            for (group in groups) {
                if (group.id == conversationId) {
                    Log.d(TAG, "Found Group: $conversationId")
                    return group
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error listing Groups: ${e.message}")
        }

        return null
    }
}

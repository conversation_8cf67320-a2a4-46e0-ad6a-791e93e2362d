package com.toii.ai.toii_xmtp_flutter

import androidx.annotation.NonNull
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.EventChannel.StreamHandler
import kotlinx.coroutines.*
import com.toii.ai.toii_xmtp_flutter.handlers.*

/** ToiiXmtpFlutterPlugin */
class ToiiXmtpFlutterPlugin: FlutterPlugin, MethodCallHandler, StreamHandler {
  private lateinit var methodChannel: MethodChannel
  private lateinit var eventChannel: EventChannel

  private val mainScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

  private val streamManager = StreamManager()

  private lateinit var clientHandler: ClientHandler
  private lateinit var conversationHandler: ConversationHandler
  private lateinit var messageHandler: MessageHandler
  private lateinit var groupHandler: GroupHandler
  private lateinit var dmHandler: DmHandler
  private lateinit var attachmentHandler: AttachmentHandler

  override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    methodChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "toii_xmtp_flutter")
    methodChannel.setMethodCallHandler(this)

    eventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "toii_xmtp_flutter_events")
    eventChannel.setStreamHandler(this)

    clientHandler = ClientHandler(flutterPluginBinding.applicationContext)
    conversationHandler = ConversationHandler()
    messageHandler = MessageHandler()
    groupHandler = GroupHandler()
    dmHandler = DmHandler()
    attachmentHandler = AttachmentHandler()
  }

  override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
    when (call.method) {
      "getPlatformVersion" -> result.success("Android ${android.os.Build.VERSION.RELEASE}")

      "createClient" -> clientHandler.createClient(call, result, mainScope)
      "getInboxId" -> clientHandler.getInboxId(call, result)
      "getInboxIdFromIdentity" -> clientHandler.getInboxIdFromIdentity(call, result, mainScope)

      "listConversations" -> conversationHandler.listConversations(call, result, mainScope)
      "syncConversations" -> conversationHandler.syncConversations(call, result, mainScope)
      "streamConversations" -> conversationHandler.streamConversations(call, result, eventChannel, mainScope)

      "createGroup" -> groupHandler.createGroup(call, result, mainScope)
      "syncGroup" -> groupHandler.syncGroup(call, result, mainScope)
      "addMembers" -> groupHandler.addMembers(call, result, mainScope)
      "removeMembers" -> groupHandler.removeMembers(call, result, mainScope)

      "findOrCreateDm" -> dmHandler.findOrCreateDm(call, result, mainScope)
      "listDms" -> dmHandler.listDms(call, result, mainScope)

      "sendMessage" -> messageHandler.sendMessage(call, result, mainScope)
      "getMessages" -> messageHandler.getMessages(call, result, mainScope)
      "listMessages" -> messageHandler.listMessages(call, result, mainScope)
      "streamMessages" -> handleStreamMessages(call, result)

      "sendMultipleRemoteAttachments" -> attachmentHandler.sendMultipleRemoteAttachments(call, result, mainScope)
      "decryptMultipleRemoteAttachments" -> attachmentHandler.decryptMultipleRemoteAttachments(call, result, mainScope)

      else -> result.notImplemented()
    }
  }

  override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
    streamManager.stopAllStreams()
    methodChannel.setMethodCallHandler(null)
    eventChannel.setStreamHandler(null)
    mainScope.cancel()
  }

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    streamManager.setEventSink(events)

    val args = arguments as? Map<String, Any>
    val streamType = args?.get("streamType") as? String

    when (streamType) {
      StreamManager.STREAM_TYPE_MESSAGES -> {
        val conversationId = args["conversationId"] as? String
        if (conversationId != null) {
          streamManager.startMessageStream(conversationId, mainScope)
        } else {
          events?.error("INVALID_ARGUMENT", "conversationId is required for message streams", null)
        }
      }
      StreamManager.STREAM_TYPE_CONVERSATIONS -> {
        streamManager.startConversationStream(mainScope)
      }
      else -> {
        events?.error("INVALID_ARGUMENT", "streamType is required", null)
      }
    }
  }

  override fun onCancel(arguments: Any?) {
    val args = arguments as? Map<String, Any>
    val streamType = args?.get("streamType") as? String

    when (streamType) {
      StreamManager.STREAM_TYPE_MESSAGES -> {
        val conversationId = args["conversationId"] as? String
        if (conversationId != null) {
          streamManager.stopMessageStream(conversationId)
        }
      }
      StreamManager.STREAM_TYPE_CONVERSATIONS -> {
        streamManager.stopConversationStream()
      }
      else -> {
        streamManager.stopAllStreams()
      }
    }
  }

  private fun handleStreamMessages(call: MethodCall, result: Result) {
    val conversationId = call.argument<String>("conversationId")
    if (conversationId == null) {
      result.error("INVALID_ARGUMENT", "conversationId is required", null)
      return
    }

    result.success(true)
  }
}

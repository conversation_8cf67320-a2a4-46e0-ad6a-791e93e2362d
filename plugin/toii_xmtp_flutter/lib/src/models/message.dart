import 'dart:convert';

import 'remote_attachment.dart';

/// XMTP Message model
class XmtpMessage {
  final String id;
  final String conversationId;
  final String senderInboxId;
  final String content;
  final String contentType;
  final DateTime sentAt;
  final MessageDeliveryStatus deliveryStatus;
  final Map<String, dynamic>? metadata;

  const XmtpMessage({
    required this.id,
    required this.conversationId,
    required this.senderInboxId,
    required this.content,
    required this.contentType,
    required this.sentAt,
    required this.deliveryStatus,
    this.metadata,
  });

  factory XmtpMessage.fromMap(Map<String, dynamic> map) {
    // Validate required fields
    final id = map['id'] as String?;
    final conversationId = map['conversationId'] as String?;
    final senderInboxId = map['senderInboxId'] as String?;
    final content = map['content'] as String?;
    final contentType = map['contentType'] as String?;
    final sentAt = map['sentAt'] as int?;
    final deliveryStatus = map['deliveryStatus'] as String?;

    if (id == null) {
      throw ArgumentError('XmtpMessage.fromMap: id cannot be null');
    }
    if (conversationId == null) {
      throw ArgumentError('XmtpMessage.fromMap: conversationId cannot be null');
    }
    if (senderInboxId == null) {
      throw ArgumentError('XmtpMessage.fromMap: senderInboxId cannot be null');
    }
    if (content == null) {
      throw ArgumentError('XmtpMessage.fromMap: content cannot be null');
    }
    if (contentType == null) {
      throw ArgumentError('XmtpMessage.fromMap: contentType cannot be null');
    }
    if (sentAt == null) {
      throw ArgumentError('XmtpMessage.fromMap: sentAt cannot be null');
    }
    if (deliveryStatus == null) {
      throw ArgumentError('XmtpMessage.fromMap: deliveryStatus cannot be null');
    }

    return XmtpMessage(
      id: id,
      conversationId: conversationId,
      senderInboxId: senderInboxId,
      content: content,
      contentType: contentType,
      sentAt: DateTime.fromMillisecondsSinceEpoch(sentAt),
      deliveryStatus: MessageDeliveryStatus.fromString(deliveryStatus),
      metadata:
          map['metadata'] != null
              ? Map<String, dynamic>.from(map['metadata'] as Map)
              : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'conversationId': conversationId,
      'senderInboxId': senderInboxId,
      'content': content,
      'contentType': contentType,
      'sentAt': sentAt.millisecondsSinceEpoch,
      'deliveryStatus': deliveryStatus.value,
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'XmtpMessage(id: $id, senderInboxId: $senderInboxId, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is XmtpMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Check if this message contains multiple remote attachments
  bool get hasMultipleRemoteAttachments {
    return contentType == XmtpContentTypes.multipleRemoteAttachment;
  }

  /// Check if this message contains a single remote attachment
  bool get hasRemoteAttachment {
    return contentType == XmtpContentTypes.remoteAttachment;
  }

  /// Check if this message contains any attachments
  bool get hasAttachments {
    return contentType == XmtpContentTypes.attachment;
  }

  /// Check if this message is text only
  bool get isTextMessage {
    return contentType == XmtpContentTypes.text || contentType == 'text/plain';
  }

  /// Parse multiple remote attachments from content
  MultipleRemoteAttachment? get multipleRemoteAttachments {
    if (!hasMultipleRemoteAttachments) return null;
    try {
      // Content should be JSON string for multiple remote attachments
      if (content.startsWith('{')) {
        final Map<String, dynamic> contentMap = Map<String, dynamic>.from(
          jsonDecode(content),
        );
        return MultipleRemoteAttachment.fromJson(contentMap);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}

/// Message delivery status
enum MessageDeliveryStatus {
  all,
  published,
  unpublished,
  failed;

  String get value {
    switch (this) {
      case MessageDeliveryStatus.all:
        return 'all';
      case MessageDeliveryStatus.published:
        return 'published';
      case MessageDeliveryStatus.unpublished:
        return 'unpublished';
      case MessageDeliveryStatus.failed:
        return 'failed';
    }
  }

  static MessageDeliveryStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'all':
        return MessageDeliveryStatus.all;
      case 'published':
        return MessageDeliveryStatus.published;
      case 'unpublished':
        return MessageDeliveryStatus.unpublished;
      case 'failed':
        return MessageDeliveryStatus.failed;
      default:
        throw ArgumentError('Invalid message delivery status: $value');
    }
  }
}

/// Sort direction for message queries
enum SortDirection {
  ascending,
  descending;

  String get value {
    switch (this) {
      case SortDirection.ascending:
        return 'ascending';
      case SortDirection.descending:
        return 'descending';
    }
  }

  static SortDirection fromString(String value) {
    switch (value.toLowerCase()) {
      case 'ascending':
        return SortDirection.ascending;
      case 'descending':
        return SortDirection.descending;
      default:
        throw ArgumentError('Invalid sort direction: $value');
    }
  }
}

/// Send options for messages
class SendOptions {
  final String? contentType;
  final CompressionType? compression;

  const SendOptions({this.contentType, this.compression});

  Map<String, dynamic> toMap() {
    return {'contentType': contentType, 'compression': compression?.value};
  }

  factory SendOptions.fromMap(Map<String, dynamic> map) {
    return SendOptions(
      contentType: map['contentType'] as String?,
      compression:
          map['compression'] != null
              ? CompressionType.fromString(map['compression'] as String)
              : null,
    );
  }
}

/// Compression types for messages
enum CompressionType {
  gzip;

  String get value {
    switch (this) {
      case CompressionType.gzip:
        return 'gzip';
    }
  }

  static CompressionType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'gzip':
        return CompressionType.gzip;
      default:
        throw ArgumentError('Invalid compression type: $value');
    }
  }
}

/// Message query options
class MessageQueryOptions {
  final int? limit;
  final DateTime? beforeNs;
  final DateTime? afterNs;
  final SortDirection? direction;
  final MessageDeliveryStatus? deliveryStatus;

  const MessageQueryOptions({
    this.limit,
    this.beforeNs,
    this.afterNs,
    this.direction,
    this.deliveryStatus,
  });

  Map<String, dynamic> toMap() {
    return {
      'limit': limit,
      'beforeNs': beforeNs?.microsecondsSinceEpoch,
      'afterNs': afterNs?.microsecondsSinceEpoch,
      'direction': direction?.value,
      'deliveryStatus': deliveryStatus?.value,
    };
  }

  factory MessageQueryOptions.fromMap(Map<String, dynamic> map) {
    return MessageQueryOptions(
      limit: map['limit'] as int?,
      beforeNs:
          map['beforeNs'] != null
              ? DateTime.fromMicrosecondsSinceEpoch(map['beforeNs'] as int)
              : null,
      afterNs:
          map['afterNs'] != null
              ? DateTime.fromMicrosecondsSinceEpoch(map['afterNs'] as int)
              : null,
      direction:
          map['direction'] != null
              ? SortDirection.fromString(map['direction'] as String)
              : null,
      deliveryStatus:
          map['deliveryStatus'] != null
              ? MessageDeliveryStatus.fromString(
                map['deliveryStatus'] as String,
              )
              : null,
    );
  }
}

/// Message with reactions
class MessageWithReactions {
  final XmtpMessage message;
  final List<MessageReaction> reactions;

  const MessageWithReactions({required this.message, required this.reactions});

  factory MessageWithReactions.fromMap(Map<String, dynamic> map) {
    return MessageWithReactions(
      message: XmtpMessage.fromMap(
        Map<String, dynamic>.from(map['message'] as Map),
      ),
      reactions:
          (map['reactions'] as List)
              .map(
                (r) => MessageReaction.fromMap(
                  Map<String, dynamic>.from(r as Map),
                ),
              )
              .toList(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'message': message.toMap(),
      'reactions': reactions.map((r) => r.toMap()).toList(),
    };
  }
}

/// Message reaction
class MessageReaction {
  final String emoji;
  final String senderInboxId;
  final DateTime sentAt;

  const MessageReaction({
    required this.emoji,
    required this.senderInboxId,
    required this.sentAt,
  });

  factory MessageReaction.fromMap(Map<String, dynamic> map) {
    return MessageReaction(
      emoji: map['emoji'] as String,
      senderInboxId: map['senderInboxId'] as String,
      sentAt: DateTime.fromMillisecondsSinceEpoch(map['sentAt'] as int),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'emoji': emoji,
      'senderInboxId': senderInboxId,
      'sentAt': sentAt.millisecondsSinceEpoch,
    };
  }
}

/// Remote attachment models for XMTP multiple remote attachments

/// Individual remote attachment item
class RemoteAttachmentItem {
  final String url;
  final String nonce;
  final String contentDigest;
  final String filename;
  final String mimeType;
  final int contentLength;
  final String? secret;
  final String? salt;

  const RemoteAttachmentItem({
    required this.url,
    required this.nonce,
    required this.contentDigest,
    required this.filename,
    required this.mimeType,
    required this.contentLength,
    this.secret,
    this.salt,
  });

  factory RemoteAttachmentItem.fromJson(Map<String, dynamic> json) {
    return RemoteAttachmentItem(
      url: json['url'] as String,
      nonce: json['nonce'] as String,
      contentDigest: json['contentDigest'] as String,
      filename: json['filename'] as String,
      mimeType: json['mimeType'] as String,
      contentLength: json['contentLength'] as int,
      secret: json['secret'] as String?,
      salt: json['salt'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'nonce': nonce,
      'contentDigest': contentDigest,
      'filename': filename,
      'mimeType': mimeType,
      'contentLength': contentLength,
      if (secret != null) 'secret': secret,
      if (salt != null) 'salt': salt,
    };
  }

  @override
  String toString() {
    return 'RemoteAttachmentItem(url: $url, filename: $filename, mimeType: $mimeType, contentLength: $contentLength)';
  }
}

/// Multiple remote attachments container
class MultipleRemoteAttachment {
  final List<RemoteAttachmentItem> attachments;
  final String scheme;
  final String? secret;
  final String? salt;

  const MultipleRemoteAttachment({
    required this.attachments,
    this.scheme = 'https://',
    this.secret,
    this.salt,
  });

  factory MultipleRemoteAttachment.fromJson(Map<String, dynamic> json) {
    final attachmentsList = json['attachments'] as List<dynamic>;
    final attachments = attachmentsList
        .map((item) => RemoteAttachmentItem.fromJson(item as Map<String, dynamic>))
        .toList();

    return MultipleRemoteAttachment(
      attachments: attachments,
      scheme: json['scheme'] as String? ?? 'https://',
      secret: json['secret'] as String?,
      salt: json['salt'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'attachments': attachments.map((item) => item.toJson()).toList(),
      'scheme': scheme,
      if (secret != null) 'secret': secret,
      if (salt != null) 'salt': salt,
    };
  }

  @override
  String toString() {
    return 'MultipleRemoteAttachment(attachments: ${attachments.length}, scheme: $scheme)';
  }
}

/// Decrypted local attachment for display
class DecryptedLocalAttachment {
  final String fileUri;
  final String filename;
  final String mimeType;
  final int? contentLength;

  const DecryptedLocalAttachment({
    required this.fileUri,
    required this.filename,
    required this.mimeType,
    this.contentLength,
  });

  factory DecryptedLocalAttachment.fromJson(Map<String, dynamic> json) {
    return DecryptedLocalAttachment(
      fileUri: json['fileUri'] as String,
      filename: json['filename'] as String,
      mimeType: json['mimeType'] as String,
      contentLength: json['contentLength'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fileUri': fileUri,
      'filename': filename,
      'mimeType': mimeType,
      if (contentLength != null) 'contentLength': contentLength,
    };
  }

  @override
  String toString() {
    return 'DecryptedLocalAttachment(fileUri: $fileUri, filename: $filename, mimeType: $mimeType)';
  }
}

/// Attachment metadata for encryption/decryption
class AttachmentMetadata {
  final String secret;
  final String salt;
  final String nonce;
  final String contentDigest;
  final String filename;
  final String mimeType;
  final int contentLength;

  const AttachmentMetadata({
    required this.secret,
    required this.salt,
    required this.nonce,
    required this.contentDigest,
    required this.filename,
    required this.mimeType,
    required this.contentLength,
  });

  factory AttachmentMetadata.fromJson(Map<String, dynamic> json) {
    return AttachmentMetadata(
      secret: json['secret'] as String,
      salt: json['salt'] as String,
      nonce: json['nonce'] as String,
      contentDigest: json['contentDigest'] as String,
      filename: json['filename'] as String,
      mimeType: json['mimeType'] as String,
      contentLength: json['contentLength'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'secret': secret,
      'salt': salt,
      'nonce': nonce,
      'contentDigest': contentDigest,
      'filename': filename,
      'mimeType': mimeType,
      'contentLength': contentLength,
    };
  }

  @override
  String toString() {
    return 'AttachmentMetadata(filename: $filename, mimeType: $mimeType, contentLength: $contentLength)';
  }
}

/// Content types for XMTP messages
class XmtpContentTypes {
  static const String text = 'xmtp.org/text:1.0';
  static const String attachment = 'xmtp.org/attachment:1.0';
  static const String remoteAttachment = 'xmtp.org/remoteStaticAttachment:1.0';
  static const String multipleRemoteAttachment = 'xmtp.org/multiRemoteStaticContent:1.0';
}

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'src/models/models.dart';
import 'toii_xmtp_flutter_platform_interface.dart';

class MethodChannelToiiXmtpFlutter extends ToiiXmtpFlutterPlatform {
  @visibleForTesting
  final methodChannel = const MethodChannel('toii_xmtp_flutter');

  @visibleForTesting
  final eventChannel = const EventChannel('toii_xmtp_flutter_events');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>(
      'getPlatformVersion',
    );
    return version;
  }

  @override
  Future<XmtpClient> createClient({
    required String privateKey,
    ClientOptions? options,
  }) async {
    // Input validation
    if (privateKey.isEmpty) {
      throw const ClientCreationException('Private key cannot be empty');
    }

    if (!privateKey.startsWith('0x') &&
        privateKey.length != 64 &&
        privateKey.length != 66) {
      throw const ClientCreationException('Invalid private key format');
    }

    try {
      final arguments = {'privateKey': privateKey, ...?options?.toMap()};

      final result = await methodChannel.invokeMethod<Map<Object?, Object?>>(
        'createClient',
        arguments,
      );
      if (result == null) {
        throw const ClientCreationException(
          'Failed to create client: null result',
        );
      }

      return XmtpClient.fromMap(Map<String, dynamic>.from(result));
    } on PlatformException catch (e) {
      throw ClientCreationException(
        'Platform error: ${e.message}',
        code: e.code,
        details: e.details,
      );
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw ClientCreationException('Failed to create client: $e');
    }
  }

  @override
  Future<String> getInboxId() async {
    try {
      final result = await methodChannel.invokeMethod<String>('getInboxId');
      if (result == null) {
        throw const XmtpException('Failed to get inbox ID: null result');
      }
      return result;
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw XmtpException('Failed to get inbox ID: $e');
    }
  }

  @override
  Future<String> getInboxIdFromIdentity(
    String identity, {
    String environment = 'dev',
  }) async {
    try {
      final result = await methodChannel.invokeMethod<String>(
        'getInboxIdFromIdentity',
        {'identity': identity, 'environment': environment},
      );
      if (result == null) {
        throw const XmtpException(
          'Failed to get inbox ID from identity: null result',
        );
      }
      return result;
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw XmtpException('Failed to get inbox ID from identity: $e');
    }
  }

  @override
  Future<List<Conversation>> listConversations({
    bool includeGroups = true,
    bool includeDms = true,
  }) async {
    try {
      final result = await methodChannel.invokeMethod<List<Object?>>(
        'listConversations',
        {'includeGroups': includeGroups, 'includeDms': includeDms},
      );

      if (result == null) {
        throw const ConversationException(
          'Failed to list conversations: null result',
        );
      }

      return result.map((item) {
        final map = Map<String, dynamic>.from(item as Map);
        final type = ConversationType.fromString(map['type'] as String);

        switch (type) {
          case ConversationType.group:
            return Group.fromMap(map);
          case ConversationType.dm:
            return Dm.fromMap(map);
        }
      }).toList();
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw ConversationException('Failed to list conversations: $e');
    }
  }

  @override
  Future<void> syncConversations() async {
    try {
      await methodChannel.invokeMethod<void>('syncConversations');
    } catch (e) {
      throw ConversationException('Failed to sync conversations: $e');
    }
  }

  @override
  Stream<Conversation> streamConversations({
    bool includeGroups = true,
    bool includeDms = true,
  }) {
    return eventChannel
        .receiveBroadcastStream({
          'streamType': 'conversations',
          'includeGroups': includeGroups,
          'includeDms': includeDms,
        })
        .where((event) {
          // Filter events to only include conversations
          final eventMap = Map<String, dynamic>.from(event as Map);
          return eventMap['streamType'] == 'conversations';
        })
        .map((event) {
          final map = Map<String, dynamic>.from(event as Map);
          final type = ConversationType.fromString(map['type'] as String);

          switch (type) {
            case ConversationType.group:
              return Group.fromMap(map);
            case ConversationType.dm:
              return Dm.fromMap(map);
          }
        });
  }

  @override
  Future<Group> createGroup({
    required List<String> memberInboxIds,
    String? groupName,
    String? groupDescription,
    String? groupImageUrl,
  }) async {
    try {
      final result = await methodChannel
          .invokeMethod<Map<Object?, Object?>>('createGroup', {
            'memberInboxIds': memberInboxIds,
            'groupName': groupName,
            'groupDescription': groupDescription,
            'groupImageUrl': groupImageUrl,
          });

      if (result == null) {
        throw const ConversationException(
          'Failed to create group: null result',
        );
      }

      return Group.fromMap(Map<String, dynamic>.from(result));
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw ConversationException('Failed to create group: $e');
    }
  }

  @override
  Future<List<Group>> listGroups() async {
    try {
      final result = await methodChannel.invokeMethod<List<Object?>>(
        'listGroups',
      );

      if (result == null) {
        throw const ConversationException('Failed to list groups: null result');
      }

      return result
          .map((item) => Group.fromMap(Map<String, dynamic>.from(item as Map)))
          .toList();
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw ConversationException('Failed to list groups: $e');
    }
  }

  @override
  Future<void> syncGroup(String groupId) async {
    try {
      await methodChannel.invokeMethod<void>('syncGroup', {'groupId': groupId});
    } catch (e) {
      throw ConversationException('Failed to sync group: $e');
    }
  }

  @override
  Future<void> addMembers(String groupId, List<String> memberInboxIds) async {
    try {
      await methodChannel.invokeMethod<void>('addMembers', {
        'groupId': groupId,
        'memberInboxIds': memberInboxIds,
      });
    } catch (e) {
      throw ConversationException('Failed to add members: $e');
    }
  }

  @override
  Future<void> removeMembers(
    String groupId,
    List<String> memberInboxIds,
  ) async {
    try {
      await methodChannel.invokeMethod<void>('removeMembers', {
        'groupId': groupId,
        'memberInboxIds': memberInboxIds,
      });
    } catch (e) {
      throw ConversationException('Failed to remove members: $e');
    }
  }

  @override
  Future<void> addAdmin(String groupId, String inboxId) async {
    try {
      await methodChannel.invokeMethod<void>('addAdmin', {
        'groupId': groupId,
        'inboxId': inboxId,
      });
    } catch (e) {
      throw PermissionException('Failed to add admin: $e');
    }
  }

  @override
  Future<void> removeAdmin(String groupId, String inboxId) async {
    try {
      await methodChannel.invokeMethod<void>('removeAdmin', {
        'groupId': groupId,
        'inboxId': inboxId,
      });
    } catch (e) {
      throw PermissionException('Failed to remove admin: $e');
    }
  }

  @override
  Future<void> addSuperAdmin(String groupId, String inboxId) async {
    try {
      await methodChannel.invokeMethod<void>('addSuperAdmin', {
        'groupId': groupId,
        'inboxId': inboxId,
      });
    } catch (e) {
      throw PermissionException('Failed to add super admin: $e');
    }
  }

  @override
  Future<void> removeSuperAdmin(String groupId, String inboxId) async {
    try {
      await methodChannel.invokeMethod<void>('removeSuperAdmin', {
        'groupId': groupId,
        'inboxId': inboxId,
      });
    } catch (e) {
      throw PermissionException('Failed to remove super admin: $e');
    }
  }

  @override
  Future<void> updateGroupName(String groupId, String name) async {
    try {
      await methodChannel.invokeMethod<void>('updateGroupName', {
        'groupId': groupId,
        'name': name,
      });
    } catch (e) {
      throw ConversationException('Failed to update group name: $e');
    }
  }

  @override
  Future<void> updateGroupDescription(
    String groupId,
    String description,
  ) async {
    try {
      await methodChannel.invokeMethod<void>('updateGroupDescription', {
        'groupId': groupId,
        'description': description,
      });
    } catch (e) {
      throw ConversationException('Failed to update group description: $e');
    }
  }

  @override
  Future<void> updateGroupImageUrl(String groupId, String imageUrl) async {
    try {
      await methodChannel.invokeMethod<void>('updateGroupImageUrl', {
        'groupId': groupId,
        'imageUrl': imageUrl,
      });
    } catch (e) {
      throw ConversationException('Failed to update group image URL: $e');
    }
  }

  @override
  Future<void> updateConsentState(
    String groupId,
    ConsentState consentState,
  ) async {
    try {
      await methodChannel.invokeMethod<void>('updateConsentState', {
        'groupId': groupId,
        'consentState': consentState.value,
      });
    } catch (e) {
      throw ConversationException('Failed to update consent state: $e');
    }
  }

  @override
  Future<void> updateDisappearingMessageSettings(
    String groupId,
    DisappearingMessageSettings settings,
  ) async {
    try {
      await methodChannel
          .invokeMethod<void>('updateDisappearingMessageSettings', {
            'groupId': groupId,
            'disappearStartingAt':
                settings.disappearStartingAt.millisecondsSinceEpoch,
            'retentionDuration': settings.retentionDuration.inMilliseconds,
          });
    } catch (e) {
      throw ConversationException(
        'Failed to update disappearing message settings: $e',
      );
    }
  }

  @override
  Future<void> clearDisappearingMessageSettings(String groupId) async {
    try {
      await methodChannel.invokeMethod<void>(
        'clearDisappearingMessageSettings',
        {'groupId': groupId},
      );
    } catch (e) {
      throw ConversationException(
        'Failed to clear disappearing message settings: $e',
      );
    }
  }

  @override
  Future<Dm> findOrCreateDm(String targetInboxId) async {
    try {
      final result = await methodChannel.invokeMethod<Map<Object?, Object?>>(
        'findOrCreateDm',
        {'targetInboxId': targetInboxId},
      );

      if (result == null) {
        throw const ConversationException(
          'Failed to find or create DM: null result',
        );
      }

      return Dm.fromMap(Map<String, dynamic>.from(result));
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw ConversationException('Failed to find or create DM: $e');
    }
  }

  @override
  Future<List<Dm>> listDms() async {
    try {
      final result = await methodChannel.invokeMethod<List<Object?>>('listDms');

      if (result == null) {
        throw const ConversationException('Failed to list DMs: null result');
      }

      return result
          .map((item) => Dm.fromMap(Map<String, dynamic>.from(item as Map)))
          .toList();
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw ConversationException('Failed to list DMs: $e');
    }
  }

  @override
  Future<String> sendMessage({
    required String conversationId,
    required String content,
    SendOptions? options,
  }) async {
    // Input validation
    if (conversationId.isEmpty) {
      throw const MessageException('Conversation ID cannot be empty');
    }

    if (content.isEmpty) {
      throw const MessageException('Message content cannot be empty');
    }

    if (content.length > 10000) {
      throw const MessageException(
        'Message content too long (max 10000 characters)',
      );
    }

    try {
      final arguments = {
        'conversationId': conversationId,
        'content': content,
        ...?options?.toMap(),
      };

      final result = await methodChannel.invokeMethod<String>(
        'sendMessage',
        arguments,
      );
      if (result == null) {
        throw const MessageException('Failed to send message: null result');
      }

      return result;
    } on PlatformException catch (e) {
      throw MessageException(
        'Platform error: ${e.message}',
        code: e.code,
        details: e.details,
      );
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw MessageException('Failed to send message: $e');
    }
  }

  @override
  Future<List<XmtpMessage>> getMessages(
    String conversationId, {
    MessageQueryOptions? options,
  }) async {
    try {
      final arguments = {
        'conversationId': conversationId,
        ...?options?.toMap(),
      };

      final result = await methodChannel.invokeMethod<List<Object?>>(
        'getMessages',
        arguments,
      );

      if (result == null) {
        throw const MessageException('Failed to get messages: null result');
      }

      return result
          .map(
            (item) =>
                XmtpMessage.fromMap(Map<String, dynamic>.from(item as Map)),
          )
          .toList();
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw MessageException('Failed to get messages: $e');
    }
  }

  @override
  Future<List<MessageWithReactions>> getMessagesWithReactions(
    String conversationId, {
    int limit = 50,
  }) async {
    try {
      final result = await methodChannel.invokeMethod<List<Object?>>(
        'getMessagesWithReactions',
        {'conversationId': conversationId, 'limit': limit},
      );

      if (result == null) {
        throw const MessageException(
          'Failed to get messages with reactions: null result',
        );
      }

      return result
          .map(
            (item) => MessageWithReactions.fromMap(
              Map<String, dynamic>.from(item as Map),
            ),
          )
          .toList();
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw MessageException('Failed to get messages with reactions: $e');
    }
  }

  @override
  Stream<XmtpMessage> streamMessages(String conversationId) {
    return eventChannel
        .receiveBroadcastStream({
          'streamType': 'messages',
          'conversationId': conversationId,
        })
        .where((event) {
          // Filter events to only include messages for this conversation
          final eventMap = Map<String, dynamic>.from(event as Map);
          return eventMap['streamType'] == 'messages' &&
              eventMap['conversationId'] == conversationId;
        })
        .map(
          (event) =>
              XmtpMessage.fromMap(Map<String, dynamic>.from(event as Map)),
        );
  }

  @override
  Future<String> prepareMessage(String conversationId, String content) async {
    try {
      final result = await methodChannel.invokeMethod<String>(
        'prepareMessage',
        {'conversationId': conversationId, 'content': content},
      );

      if (result == null) {
        throw const MessageException('Failed to prepare message: null result');
      }

      return result;
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw MessageException('Failed to prepare message: $e');
    }
  }

  @override
  Future<void> publishMessages(String conversationId) async {
    try {
      await methodChannel.invokeMethod<void>('publishMessages', {
        'conversationId': conversationId,
      });
    } catch (e) {
      throw MessageException('Failed to publish messages: $e');
    }
  }

  @override
  Future<String> sendMultipleRemoteAttachments({
    required String conversationId,
    required List<String> attachmentUrls,
  }) async {
    try {
      final result = await methodChannel.invokeMethod<String>(
        'sendMultipleRemoteAttachments',
        {'conversationId': conversationId, 'attachmentUrls': attachmentUrls},
      );

      if (result == null) {
        throw const MessageException(
          'Failed to send multiple remote attachments: null result',
        );
      }

      return result;
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw MessageException('Failed to send multiple remote attachments: $e');
    }
  }

  @override
  Future<List<DecryptedLocalAttachment>> decryptMultipleRemoteAttachments({
    required MultipleRemoteAttachment attachmentData,
  }) async {
    try {
      final result = await methodChannel.invokeMethod<List<dynamic>>(
        'decryptMultipleRemoteAttachments',
        {'attachmentData': attachmentData.toJson()},
      );

      if (result == null) {
        throw const MessageException(
          'Failed to decrypt multiple remote attachments: null result',
        );
      }

      return result
          .map(
            (item) => DecryptedLocalAttachment.fromJson(
              Map<String, dynamic>.from(item as Map),
            ),
          )
          .toList();
    } catch (e) {
      if (e is XmtpException) rethrow;
      throw MessageException(
        'Failed to decrypt multiple remote attachments: $e',
      );
    }
  }
}

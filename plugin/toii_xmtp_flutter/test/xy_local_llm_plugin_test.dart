import 'package:flutter_test/flutter_test.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter_platform_interface.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockToiiXmtpFlutterPluginPlatform
    with MockPlatformInterfaceMixin
    implements ToiiXmtpFlutterPluginPlatform {
  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final ToiiXmtpFlutterPluginPlatform initialPlatform =
      ToiiXmtpFlutterPluginPlatform.instance;

  test('$MethodChannelToiiXmtpFlutterPlugin is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelToiiXmtpFlutterPlugin>());
  });

  test('getPlatformVersion', () async {
    ToiiXmtpFlutterPlugin ToiiXmtpFlutterPlugin = ToiiXmtpFlutterPlugin();
    MockToiiXmtpFlutterPluginPlatform fakePlatform =
        MockToiiXmtpFlutterPluginPlatform();
    ToiiXmtpFlutterPluginPlatform.instance = fakePlatform;

    expect(await ToiiXmtpFlutterPlugin.getPlatformVersion(), '42');
  });
}
